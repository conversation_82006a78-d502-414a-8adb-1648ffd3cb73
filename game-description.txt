Overview

Title: Stella<PERSON>
Genre: 2D Platformer
Theme: Space exploration. The player is a cute astronaut hopping between floating asteroids in a starry nebula. The vibe is relaxing yet exciting, with soft glow effects, particle stars, and chill music (assume free assets).
Objective: Collect all 10 stars scattered across the level while avoiding falling off platforms or touching spiky comets. Reach the portal at the end to win. If you die, restart with a score penalty.
Duration: Short single level (5-10 minutes to complete), but easy to extend for testing.
Controls:

Left/Right: Move.
Space: Jump (variable height based on hold time).
Shift: Dash (short burst for gaps).


Key Features for Vibe Coding:

Procedural element: Random star placements on load for replayability.
Atmospheric effects: Simple particle system for star trails and jump dust.
Sound/VFX: Jump boing, collect sparkle, death poof (placeholders for assets).
Scoring: Stars collected + time bonus.
Game over/win screens with fade transitions.


Technical Specs:

Godot 4.3+ (uses CharacterBody2D for player physics, TileMap for platforms).
Scenes: Main level scene, Player node, Star collectible, Comet enemy, Portal goal, HUD canvas.
Total code: ~500 lines across 6 scripts (player.gd ~200 lines, main.gd ~100, others ~50 each, with comments for clarity).
Assets needed: Simple sprites (astronaut, star, comet, portal, tile set for platforms)—can be placeholders or free from Godot Asset Library.
Why good for vibe coding: Encourages creative tweaks like adding power-ups or procedural generation without complex architecture. AI can "vibe" with prompts like "Make the jump feel floaty in space."



Detailed Mechanics

Player:

Movement: Horizontal speed 300 px/s, jump force 500, gravity 980.
Dash: Temporary speed boost (500 px/s for 0.5s, cooldown 2s).
Animation: Idle, walk, jump, dash (use AnimatedSprite2D).
Collision: Dies on comet touch or falling off screen.


Level Layout:

TileMap with floating platforms (e.g., 10x5 grid, gaps for jumping).
10 stars placed randomly on platforms.
3-5 comets patrolling horizontally on paths.
Portal at end, activated when all stars collected.


Enemies (Comets):

Move left/right at constant speed, reverse on edge.
Kill player on contact.


Collectibles (Stars):

Play animation/sound on collect, add to score.
Disappear after pickup.


UI/HUD:

Score display (stars collected / 10).
Win/Game Over messages with restart button.


Game Flow:

Start: Player spawns at left, level loads.
Win: All stars collected, touch portal → fade to win screen.
Lose: Die → fade to game over, restart level.
Vibe elements: Background parallax stars, subtle screen shake on dash.



Implementation Notes for AI Testing

Use Godot's built-in nodes: CharacterBody2D for player, Area2D for stars/portal/comets, TileMap for level, CanvasLayer for HUD.
Scripts structure:

player.gd: Handles input, physics, animations, signals for die/collect.
comet.gd: Simple patrol logic.
star.gd: Collection logic.
portal.gd: Win condition check.
hud.gd: Update UI, handle buttons.
main.gd: Level setup, spawning, game state (start, win, lose), random star placement.
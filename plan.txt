Step 1: Project Setup (Prep Time: 5-10 mins)

Download and open Godot 4.3+ if not already installed.
Create a new project named "StellarHopper".
In Project Settings > Input Map, add actions:

"move_left" (key: A or Left Arrow)
"move_right" (key: D or Right Arrow)
"jump" (key: Space)
"dash" (key: Shift)


Import placeholder assets (use <PERSON><PERSON>'s built-in "icon.svg" for sprites initially, or free assets from Godot Asset Library like simple pixel art for astronaut, star, comet, portal, and a tile set for platforms). Organize in folders: res://sprites/, res://sounds/.
Enable "Advanced" mode in the editor for full node access.
Goal: Empty project ready for scenes. (No code yet.)

Step 2: Create Core Scenes (Time: 20-30 mins)

Main Level Scene (main.tscn):

Root: Node2D named "Main".
Add children: TileMap (for platforms), Player (instance from player.tscn, see below), HUD (CanvasLayer from hud.tscn), and a ParallaxBackground for starry vibe (add ParallaxLayer with Sprite2D for stars).
Set TileMap with a basic tile set: Create floating platforms (e.g., 10 platforms with gaps) using a 16x16 grid. Use physics layers for collision.


Player Scene (player.tscn):

Root: CharacterBody2D.
Children: AnimatedSprite2D (for idle/walk/jump/dash animations—use frames from sprite sheet), CollisionShape2D (RectangleShape2D for hitbox).


Star Collectible Scene (star.tscn):

Root: Area2D.
Children: Sprite2D (star image), CollisionShape2D (small circle).


Comet Enemy Scene (comet.tscn):

Root: Area2D (for patrol, not full physics body).
Children: Sprite2D (comet image), CollisionShape2D.


Portal Scene (portal.tscn):

Root: Area2D.
Children: Sprite2D (portal image), CollisionShape2D.


HUD Scene (hud.tscn):

Root: CanvasLayer.
Children: Label (for score: "Stars: 0/10"), Control nodes for win/lose screens (e.g., Panel with Label and Button for restart).


Best Practice: Save all scenes in res://scenes/. Use Godot's scene instancing for reusability. (From Godot docs on 2D setup.)

Step 3: Script the Player (player.gd) (Time: 40-60 mins, ~200 lines)

Attach a new GDScript to the Player node.
Define variables: @export var speed = 300, var jump_force = 500, var gravity = 980, var dash_speed = 500, var dash_cooldown = 2.0, etc. Use @export for easy tweaking.
In _physics_process(delta):

Handle input: var direction = Input.get_axis("move_left", "move_right").
Apply movement: velocity.x = direction * speed.
Gravity: if not is_on_floor(): velocity.y += gravity * delta.
Jump: if Input.is_action_just_pressed("jump") and is_on_floor(): velocity.y = -jump_force. Add variable height: If held, reduce fall speed slightly for "floaty" space vibe.
Dash: If "dash" pressed and cooldown ready, set velocity.x *= dash_speed for 0.5s, then cooldown timer.


Animations: Play "walk" if moving, "jump" if in air, "idle" otherwise. Use animated_sprite.play("animation_name").
Signals: Connect body_entered from Area2D (for comets/stars) to handle death/collect. Emit custom signals like died or collected_star.
Collision: Use move_and_slide() at the end for physics.
Vibe Tip: Add particle effect (GPUParticles2D child) for jump dust—emit on jump/dash for atmospheric feel.
Test: Instance player in a temp scene with a floor; ensure movement feels smooth and "vibey" (adjust constants iteratively). (Modular movement from tutorials.)

Step 4: Script Enemies and Collectibles (Time: 20-30 mins, ~150 lines total)

star.gd: On body_entered, play collect sound/animation, increment score (emit signal to Main), queue_free().
comet.gd: In _process(delta), move horizontally: position.x += speed * direction * delta. Reverse direction on edge (use RayCast2D for detection or simple position checks). On body_entered with player, emit player_died signal.
portal.gd: On body_entered, check if all stars collected (via global or signal), then trigger win.
Keep scripts short: Focus on core logic, no extras.
Test: Add to main scene; verify collection/death works.

Step 5: Script Main Level and Game Flow (main.gd) (Time: 30-40 mins, ~100 lines)

Attach script to Main node.
Variables: var stars_collected = 0, var total_stars = 10.
On ready: Spawn 10 stars randomly on platforms (use instance() and add_child(), position via random offsets on TileMap coords). Spawn 3-5 comets at set paths.
Signals: Connect from player (died → game over), stars (collected → update score), portal (entered → win if stars == 10).
Procedural Vibe: Use randf_range() for star positions to add replayability.
Game States: Functions for start (reset positions), win (show HUD win screen, fade transition), lose (show game over, restart button).
Add background parallax: In _process, scroll slowly for space feel.
HUD Updates: In hud.gd, update Label text on signal; add Button for restart (get_tree().reload_current_scene()).
Test: Play full level; ensure flow (collect, die, win) works without exceeding line limit—comment generously for clarity.

Step 6: Add Polish and Effects (Time: 20-30 mins, ~50 lines)

Sounds: Add AudioStreamPlayer2D to player/star for jump/collect (use built-in or free samples).
Particles: For star trails or death poof (add GPUParticles2D to relevant nodes, emit on events).
Screen Shake: Simple function in main.gd: On dash/death, tween camera offset randomly for 0.2s.
Fades: Use AnimationPlayer on a ColorRect (black) for win/lose transitions.
Vibe Check: Playtest for "relaxing yet exciting" feel—tweak gravity to 800 for floatier jumps.

Step 7: Optimization, Testing, and Finalization (Time: 20-30 mins)

Count lines: Use Godot's script editor or external @tool; trim comments if over 500, merge simple functions.
Full Test: Run the game; collect all stars, die to comet/fall, win. Fix bugs (e.g., collision layers: Set player on layer 1, comets on 2, etc.).
Performance: Ensure no lag (Godot handles 2D well); add queue_free() for removed nodes.
Export: Optionally export to executable for final vibe test.
Iteration: If code exceeds limit, refactor (e.g., combine enemy logics). Reference tutorials for efficiency. (From complete platformer tutorials.)

Follow this plan sequentially; if stuck, query Godot docs or simulate tests.